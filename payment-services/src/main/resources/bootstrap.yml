server:
  port: 7005
spring:
  application:
    name: payment-services #服务名称 必须要有
  main:
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: ***********:8848 #服务注册中心地址
        namespace: 5139d7fa-db43-42ae-9697-509bddd0cbd6 #命名空间
      config:
        server-addr: ***********:8848 #配置中心地址
        file-extension: yaml #指定yaml格式的配置
        group: DEFAULT_GROUP
        namespace: 5139d7fa-db43-42ae-9697-509bddd0cbd6

  rabbitmq:
    host: ***********
    port: 5672
    username: admin
    password: admin
    connection-timeout: 60000
    publisher-confirm-type: correlated
    template:
      retry:
        enabled: true               # 开启生产者重试
        max-attempts: 3             # 最大重试次数
        initial-interval: 1000ms    # 初始重试间隔
