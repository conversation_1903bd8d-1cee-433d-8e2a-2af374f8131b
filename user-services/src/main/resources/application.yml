spring:
  profiles:
    active: dev
  config:
    import: optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  mvc:
    servlet:
      path: /userServices
  main:
    allow-circular-references: true
#actuator 运维配置信息
management:
  endpoints:
    web:
      exposure:
        include: "health,info"
mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  type-aliases-package: com.knet.entity

  rabbitmq:
    host: ***********
    port: 5672
    username: admin
    password: admin
    connection-timeout: 60000
    publisher-confirm-type: correlated
    template:
      retry:
        enabled: true               # 开启生产者重试
        max-attempts: 3             # 最大重试次数
        initial-interval: 1000ms    # 初始重试间隔