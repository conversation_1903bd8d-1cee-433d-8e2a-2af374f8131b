package com.knet.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.dto.req.UserAddressQueryRequest;
import com.knet.user.model.dto.req.UserAddressSaveRequest;
import com.knet.user.model.dto.rsp.UserAddressDtoResp;
import com.knet.user.model.entity.SysUserAddress;

/**
 * <AUTHOR>
 * @description 针对表【sys_user_address(用户地址表)】的数据库操作Service
 * @date 2025-05-07 14:25:18
 */
public interface ISysUserAddressService extends IService<SysUserAddress> {
    /**
     * 创建用户地址
     *
     * @param request 用户地址请求体
     * @return 返回创建结果
     */
    SysUserAddress createAddress(UserAddressSaveRequest request);

    /**
     * 查询用户地址列表
     *
     * @param request r
     * @return r
     */
    IPage<UserAddressDtoResp> listAddress(UserAddressQueryRequest request);

    /**
     * 删除用户地址
     *
     * @param id 用户地址id
     */
    void deleteUserAddress(Long id);

    /**
     * 修改用户地址
     *
     * @param id 用户地址id
     * @param request 用户地址请求体
     * @return 更新后的用户地址
     */
    SysUserAddress updateUserAddress(Long id, UserAddressSaveRequest request);
}
