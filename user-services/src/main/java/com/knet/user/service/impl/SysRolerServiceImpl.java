package com.knet.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.RoleStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.user.mapper.SysRolerMapper;
import com.knet.user.model.dto.req.RolerSaveRequest;
import com.knet.user.model.entity.SysRoler;
import com.knet.user.service.ISysRolerPermissionRelService;
import com.knet.user.service.ISysRolerService;
import com.knet.user.service.ISysUserRolerRelService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

import static com.knet.common.constants.UserServicesConstants.KNET_ROLER_LIST_EXPIRED_TIME;
import static com.knet.common.constants.UserServicesConstants.ROLER_LIST;

/**
 * <AUTHOR>
 * @date 2025/2/13 09:52
 * @description: 角色接口实现类
 */
@Service
public class SysRolerServiceImpl extends ServiceImpl<SysRolerMapper, SysRoler> implements ISysRolerService {

    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private ISysUserRolerRelService iSysUserRolerRelService;
    @Resource
    private ISysRolerPermissionRelService iRolePermissionRelService;

    @Override
    public List<SysRoler> queryListFromCache() {
        List<Object> objects = redisCacheUtil.lGet(ROLER_LIST, 0, -1);
        if (CollUtil.isEmpty(objects)) {
            LambdaQueryWrapper<SysRoler> rolerWrapper = new LambdaQueryWrapper<>();
            rolerWrapper.eq(SysRoler::getStatus, RoleStatus.ENABLE);
            List<SysRoler> sysRolerList = this.list(rolerWrapper);
            redisCacheUtil.lSet(ROLER_LIST, sysRolerList, KNET_ROLER_LIST_EXPIRED_TIME);
            return sysRolerList;
        }
        Object arrayOrCollection = objects.get(0);
        return JSONUtil.parseArray(arrayOrCollection).toList(SysRoler.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysRoler createRoler(RolerSaveRequest request) {
        try {
            SysRoler build = SysRoler.builder()
                    .code(request.getCode())
                    .name(request.getName())
                    .build();
            this.save(build);
            return build;
        } catch (Exception e) {
            throw new ServiceException("创建角色失败,name或者code已存在");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRoler(Long id) {
        if (null == id || id <= 0) {
            throw new ServiceException("角色ID不能为空,或者角色ID不合法");
        }
        //有用户关联的不可以删除
        if (iSysUserRolerRelService.countUserRolerRelById(id) > 0) {
            throw new ServiceException("有用户关联的角色不可以删除");
        }
        try {
            this.removeById(id);
            //删除缓存
            redisCacheUtil.del(ROLER_LIST);
            //删除用户角色关联
            iSysUserRolerRelService.removeByRolerId(id);
            //删除角色权限关联
            iRolePermissionRelService.removeByRolerId(id);
        } catch (Exception e) {
            throw new ServiceException("删除角色失败");
        }
    }
}
