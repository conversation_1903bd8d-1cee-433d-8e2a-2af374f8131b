package com.knet.user.service;

import com.knet.user.model.dto.req.AuthBaseRequest;
import com.knet.user.model.dto.req.AuthUserRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025/2/13 14:34
 * @description: 用户授权服务
 */
public interface IAuthService {
    /**
     * 验证用户授权
     *
     * @param request request
     * @return boolean
     */
    boolean auth(AuthBaseRequest request);

    /**
     * 用户登录
     *
     * @param request request
     * @return token
     */
    String login(AuthUserRequest request);

    /**
     * 用户退出
     *
     * @param token token
     * @param name  name
     */
    void logout(String token, String name);

    /**
     * 获取图形验证码
     *
     * @param req  request
     * @param resp response
     */
    void getCaptcha(HttpServletRequest req, HttpServletResponse resp);
}
