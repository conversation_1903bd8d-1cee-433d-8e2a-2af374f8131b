package com.knet.user.controller;

import com.knet.common.base.HttpResult;
import com.knet.user.openfeign.ApiGoodsServiceProvider;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/10 13:21
 * @description: 测试
 */
@Slf4j
@RestController
@RequestMapping
@Tag(name = "DEMO接口", description = "DEMO接口")
public class DemoController {
    @Resource
    private ApiGoodsServiceProvider apiGoodsServiceProvider;

    @Operation(summary = "openfeign测试")
    @GetMapping("/demo/test")
    public HttpResult<String> test() {
        HttpResult<String> info = apiGoodsServiceProvider.info();
        return HttpResult.ok(info.getData());
    }
}
