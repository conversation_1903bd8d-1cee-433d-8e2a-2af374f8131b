package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.user.model.dto.req.UserAddressSaveRequest;
import com.knet.user.model.dto.rsp.UserAddressDtoResp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:02
 * @description: 用户地址表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_user_address", description = "用户地址表")
@TableName("sys_user_address")
public class SysUserAddress extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 收件人全名
     */
    @Schema(description = "收件人全名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fullName;
    /**
     * 身份证号码（仅中国地址必填）
     */
    @Schema(description = "身份证号码（仅中国地址必填）")
    private String idNumber;

    /**
     * 公司名称
     */
    @Schema(description = "公司名称")
    private String companyName;

    /**
     * 国家名称（建议存储ISO国家代码）
     */
    @Schema(description = "国家名称（建议存储ISO国家代码）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String country;

    /**
     * 地址行1（街道信息）
     */
    @Schema(description = "地址行1（街道信息）")
    private String addressLine1;

    /**
     * 地址行2（补充信息）
     */
    @Schema(description = "地址行2（补充信息）")
    private String addressLine2;

    /**
     * 城市名称
     */
    @Schema(description = "城市名称")
    private String city;

    /**
     * 州/省名称
     */
    @Schema(description = "州/省名称")
    private String state;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码")
    private String zipCode;

    /**
     * 国际电话区号(如86/852)
     */
    @Schema(description = "国际电话区号(如86/852)")
    private String phonePrefix;

    /**
     * 本地电话号码(不含国际区号)
     */
    @Schema(description = "本地电话号码(不含国际区号)")
    private String phoneNumber;

    /**
     * 完整手机号
     */
    public String getFullPhoneNumber() {
        return "+" + this.phonePrefix + this.phoneNumber;
    }

    public static SysUserAddress createAddress(UserAddressSaveRequest request) {
        return SysUserAddress.builder()
                .userId(request.getUserId())
                .fullName(request.getFullName())
                .idNumber(String.valueOf(request.getIdNumber()))
                .companyName(request.getCompanyName())
                .country(request.getCountry())
                .addressLine1(request.getAddressLine1())
                .addressLine2(request.getAddressLine2())
                .city(request.getCity())
                .state(request.getState())
                .zipCode(request.getZipCode())
                .phonePrefix(request.getPhonePrefix())
                .phoneNumber(request.getPhoneNumber())
                .build();
    }

    public UserAddressDtoResp mapToUserInfoDtoResp() {
        return UserAddressDtoResp
                .builder()
                .id(this.getId())
                .userId(this.getUserId())
                .fullName(this.getFullName())
                .idNumber(String.valueOf(this.getIdNumber()))
                .companyName(this.getCompanyName())
                .country(this.getCountry())
                .addressLine1(this.getAddressLine1())
                .addressLine2(this.getAddressLine2())
                .city(this.getCity())
                .state(this.getState())
                .zipCode(this.getZipCode())
                .phonePrefix(this.getPhonePrefix())
                .phoneNumber(this.getPhoneNumber())
                .createTime(this.getCreateTime())
                .updateTime(this.getUpdateTime())
                .build();
    }
}