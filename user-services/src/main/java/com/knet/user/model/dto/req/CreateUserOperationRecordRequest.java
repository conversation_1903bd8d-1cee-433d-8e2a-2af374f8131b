package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.enums.OperationResult;
import com.knet.common.enums.UserOperationType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/20 18:20
 * @description: 创建用户操作记录请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "创建用户操作记录请求")
public class CreateUserOperationRecordRequest extends BaseRequest {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "操作者ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "操作者ID不能为空")
    private Long operatorId;

    @Schema(description = "操作者类型", example = "USER", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "操作者类型不能为空")
    private String operatorType;

    /**
     * @see UserOperationType
     */
    @Schema(description = "操作类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "操作类型不能为空")
    private UserOperationType operationType;

    /**
     * @see OperationResult
     */
    @Schema(description = "操作结果", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "操作结果不能为空")
    private OperationResult operationResult;

    @Schema(description = "操作描述", example = "用户充值100.00美元")
    private String operationDesc;

    @Schema(description = "操作详情", example = "{\"amount\":\"100.00\",\"currency\":\"USD\"}")
    private String operationDetail;

    @Schema(description = "客户端IP地址", example = "*************")
    private String clientIp;

    @Schema(description = "用户代理", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    private String userAgent;

    @Schema(description = "关联业务ID", example = "ORD-123456789012345678")
    private String businessId;

    @Schema(description = "关联业务类型", example = "WALLET")
    private String businessType;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "备注信息")
    private String remarks;
}
