//package com.knet.user.system.Interceptor;
//
//import cn.hutool.core.util.StrUtil;
//import com.knet.common.model.annotation.LoginCheck;
//import com.knet.common.model.constants.SystemConstant;
//import com.knet.user.system.exception.ServiceException;
//import com.knet.user.system.utils.JwtUtil;
//import com.knet.user.system.utils.RedisCacheUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import org.springframework.web.method.HandlerMethod;
//import org.springframework.web.servlet.HandlerInterceptor;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.lang.reflect.Method;
//
//import static com.knet.common.model.constants.UserServicesConstants.KNET_USER_TOKEN_PREFIX;
//
/// **
// * <AUTHOR> zhangxuan
// * @Description: 校验会员用户是否登录的拦截器
// * @date 2025/02/15 14:16
// */
//@Deprecated
//@Slf4j
//@Component
//public class MemberLoginInterceptor implements HandlerInterceptor {
//    @Resource
//    private JwtUtil jwtUtil;
//    @Resource
//    private RedisCacheUtil redisCacheUtil;
//
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        // 检查处理器是否是方法
//        if (handler instanceof HandlerMethod) {
//            HandlerMethod handlerMethod = (HandlerMethod) handler;
//            Method method = handlerMethod.getMethod();
//            // 检查方法或类是否有MemberLoginCheck注解注解
//            if (method.isAnnotationPresent(LoginCheck.class) || handlerMethod.getBeanType().isAnnotationPresent(LoginCheck.class)) {
//                //直接获取用户信息，若未登录或token超时，则会抛出notLogin异常
//                String header = request.getHeader(SystemConstant.TOKEN);
//                if (StrUtil.isBlank(header)) {
//                    log.error("请求头中未包含token Not login");
//                    throw new ServiceException("Not login");
//                }
//                String userIdFromToken = getUserIdFromToken(header);
//                String redisKey = String.format(KNET_USER_TOKEN_PREFIX, userIdFromToken);
//                if (!validateRedisKey(redisKey)) {
//                    log.error("Token expired");
//                    throw new ServiceException("Not login");
//                }
//            }
//        }
//        // 如果已登录或者不需要校验，返回true
//        return true;
//    }
//
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
//
//    }
//
//    /**
//     * 验证Redis中的键
//     *
//     * @param redisKey Redis键
//     * @return 验证结果
//     */
//    private Boolean validateRedisKey(String redisKey) {
//        return redisCacheUtil.hasKey(redisKey);
//    }
//
//    /**
//     * 获取用户id
//     *
//     * @param token token
//     * @return 用户id
//     */
//    private String getUserIdFromToken(String token) throws RuntimeException {
//        if (!jwtUtil.validateToken(token)) {
//            throw new RuntimeException("Invalid token");
//        }
//        return jwtUtil.getUserIdFromToken(token);
//    }
//}
