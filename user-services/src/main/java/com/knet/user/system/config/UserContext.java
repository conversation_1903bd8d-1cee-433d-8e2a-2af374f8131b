package com.knet.user.system.config;

/**
 * <AUTHOR>
 * @date 2025/4/22 15:46
 * @description: 存储用户信息
 */
public class UserContext {
    private static final ThreadLocal<String> CONTEXT = new ThreadLocal<>();

    public static void setContext(String apiKey) {
        CONTEXT.set(apiKey);
    }

    public static String getContext() {
        return CONTEXT.get();
    }

    public static void clear() {
        CONTEXT.remove();
    }
}
