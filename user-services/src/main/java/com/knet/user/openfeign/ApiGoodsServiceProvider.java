package com.knet.user.openfeign;

import com.knet.common.base.HttpResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @date 2025/2/14 11:01
 * @description: 商品服务api
 */
@FeignClient(name = "goods-services", path = "/goods")
public interface ApiGoodsServiceProvider {
    /**
     * 测试方法
     *
     * @return 测试结果
     */
    @GetMapping("/demo/test/info")
    HttpResult<String> info();
}
