package com.knet.user.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/28 10:35
 * @description: RabbitMQ健康检查器
 */
@Slf4j
@Component
@ConditionalOnClass(ConnectionFactory.class)
@ConditionalOnProperty(name = "spring.rabbitmq.enabled", havingValue = "true", matchIfMissing = false)
public class RabbitMQHealthChecker {

    @Resource
    private ConnectionFactory connectionFactory;

    @EventListener(ApplicationReadyEvent.class)
    public void checkRabbitMQConnection() {
        if (connectionFactory == null) {
            log.info("RabbitMQ未配置，跳过连接检查");
            return;
        }

        try {
            connectionFactory.createConnection().close();
            log.info("✅ RabbitMQ连接检查成功");
        } catch (Exception e) {
            log.warn("⚠️ RabbitMQ连接检查失败: {}，但应用将继续运行", e.getMessage());
            log.info("💡 提示：如需启用RabbitMQ功能，请确保RabbitMQ服务正在运行，或在配置中设置 spring.rabbitmq.enabled=true");
        }
    }
}
