package com.knet.user.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/28 10:30
 * @description: 条件化RabbitMQ配置
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "spring.rabbitmq.enabled", havingValue = "true", matchIfMissing = false)
public class ConditionalRabbitConfig {

    @Resource
    private ConnectionFactory connectionFactory;

    @PostConstruct
    public void checkRabbitConnection() {
        if (connectionFactory != null) {
            try {
                connectionFactory.createConnection();
                log.info("RabbitMQ连接成功");
            } catch (Exception e) {
                log.warn("RabbitMQ连接失败，但应用将继续启动: {}", e.getMessage());
            }
        } else {
            log.info("RabbitMQ未配置或已禁用");
        }
    }
}
