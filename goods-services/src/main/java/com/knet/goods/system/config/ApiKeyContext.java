package com.knet.goods.system.config;

/**
 * <AUTHOR>
 * @date 2025/4/9 15:16
 * @description: ApiKeyContext 类，用于存储API
 */
public class ApiKeyContext {
    private static final ThreadLocal<String> CONTEXT = new ThreadLocal<>();

    public static void setApiKey(String apiKey) {
        CONTEXT.set(apiKey);
    }

    public static String getApiKey() {
        return CONTEXT.get();
    }

    public static void clear() {
        CONTEXT.remove();
    }
}
