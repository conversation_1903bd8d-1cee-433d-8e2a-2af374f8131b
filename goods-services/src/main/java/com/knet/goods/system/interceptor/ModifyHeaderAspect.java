package com.knet.goods.system.interceptor;

import cn.hutool.core.util.StrUtil;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.exception.ServiceException;
import com.knet.goods.system.config.AccountConfig;
import com.knet.goods.system.config.ApiKeyContext;
import com.knet.goods.system.config.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import static com.knet.common.constants.SystemConstant.TOKEN;
import static com.knet.common.constants.SystemConstant.X_API_KEY;

/**
 * <AUTHOR>
 * @date 2025/4/9 15:01
 * @description: ModifyHeader注解切面
 */
@RefreshScope
@Slf4j
@Component
@Aspect
public class ModifyHeaderAspect {
    @Autowired
    private AccountConfig accountConfig;

    @Pointcut("@annotation(modifyHeader)")
    public void permissionPointCut(ModifyHeader modifyHeader) {
    }

    /**
     * @param joinPoint    joinPoint
     * @param modifyHeader modifyHeader
     */
    @Before(value = "permissionPointCut(modifyHeader)", argNames = "joinPoint,modifyHeader")
    public void checkApiKey(JoinPoint joinPoint, ModifyHeader modifyHeader) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                log.error("Request attributes is null, cannot process ModifyHeader modifyHeader");
                throw new ServiceException("请求上下文不存在");
            }
            HttpServletRequest request = attributes.getRequest();
            String headerName = modifyHeader.value();
            String headerValue = request.getHeader(headerName);
            if (modifyHeader.required() && StrUtil.isEmpty(headerValue)) {
                log.error("Required header [{}] is missing in request", headerName);
                throw new ServiceException("缺少必要的请求头: " + headerName);
            }
            //当自定义请求头是x-api-key
            if (X_API_KEY.equals(headerName)) {
                if (!StrUtil.equals(accountConfig.getKgAccessToken(), headerValue)) {
                    log.error("请求头 [{}] 的值 [{}] accessToken不正确", headerName, headerValue);
                    throw new ServiceException("accessToken不正确");
                }
                ApiKeyContext.setApiKey(accountConfig.getKgAccount());
                log.debug("Set API key [{}] from header [{}]", StrUtil.isEmpty(headerValue) ? "EMPTY" : "******", headerName);
            }
            // 其他请求头的处理逻辑
            if (TOKEN.equals(headerName)) {
                UserContext.setContext(headerValue);
            }
        } catch (Exception e) {
            if (!(e instanceof ServiceException)) {
                log.error("Failed to process header", e);
                throw new ServiceException("处理请求头时发生错误");
            }
            throw e;
        }
    }

    /**
     * 无论方法是否正常结束，都确保清理ThreadLocal
     */
    @After("@annotation(modifyHeader) || @within(modifyHeader)")
    public void clearContext(ModifyHeader modifyHeader) {
        ApiKeyContext.clear();
        log.debug("ThreadLocal context cleared");
    }
}
