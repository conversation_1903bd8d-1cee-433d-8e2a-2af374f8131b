package com.knet.goods.service;

import com.knet.goods.model.dto.third.KnetGroupGetInventoryReq;
import com.knet.goods.model.dto.third.KnetGroupGetSkuSizePlatformPriceReq;
import com.knet.goods.model.dto.third.resp.KnetInventoryDataVo;
import com.knet.goods.model.dto.third.resp.KnetProductMarketDataVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 11:11
 * @description: 第三方API调用服务 服务定义
 */
public interface IThirdApiService {

    /**
     * 从kg 获取商品市场数据
     *
     * @param req 请求体
     * @return 商品市场数据
     */
    List<KnetProductMarketDataVo> getProductMarketData(KnetGroupGetSkuSizePlatformPriceReq req);


    /**
     * 获取 kg 热门 sku 排行
     *
     * @return kg 热门 sku 排行
     */
    List<KnetProductMarketDataVo> getHotSkuRankData();

    /**
     * 从kg 获取商品库存数据
     *
     * @param req 请求体
     * @return 商品库存数据
     */
    List<KnetInventoryDataVo> getInventoryData(KnetGroupGetInventoryReq req);
}
