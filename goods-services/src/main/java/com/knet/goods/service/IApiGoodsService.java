package com.knet.goods.service;

import com.knet.goods.model.dto.req.CreateKnetProductRequest;
import com.knet.goods.model.dto.req.OffSaleKnetProductRequest;
import com.knet.goods.model.dto.req.QueryKnetProductRequest;
import com.knet.goods.model.dto.req.UpdatePriceKnetProductRequest;
import com.knet.goods.model.dto.resp.CreateKnetProductResp;
import com.knet.goods.model.dto.resp.OffSaleKnetProductResp;
import com.knet.goods.model.dto.resp.QueryKnetProductResp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 16:37
 * @description: goods服务-队外提供服务 定义
 */
public interface IApiGoodsService {

    /**
     * 创建上架商品
     *
     * @param request 创建商品请求体
     * @return 返回创建结果
     * @see com.knet.goods.model.dto.req.CreateKnetProductRequest
     */
    CreateKnetProductResp createProducts(CreateKnetProductRequest request);

    /**
     * 商品下架
     *
     * @param request 商品下架请求体
     * @return 返回下架结果
     * @see com.knet.goods.model.dto.req.CreateKnetProductRequest
     */
    OffSaleKnetProductResp offSale(OffSaleKnetProductRequest request);

    /**
     * 更新商品价格
     *
     * @param request 更新商品价格请求体
     * @return 返回更新结果
     */
    OffSaleKnetProductResp updatePrice(UpdatePriceKnetProductRequest request);

    /**
     * 查询商品
     *
     * @param request 查询商品请求体
     * @return 返回查询结果
     */
    List<QueryKnetProductResp> queryKnetProduct(QueryKnetProductRequest request);
}
