package com.knet.goods.service.impl;

import com.knet.common.annotation.DistributedLock;
import com.knet.goods.model.dto.req.CreateKnetProductRequest;
import com.knet.goods.model.dto.req.OffSaleKnetProductRequest;
import com.knet.goods.model.dto.req.QueryKnetProductRequest;
import com.knet.goods.model.dto.req.UpdatePriceKnetProductRequest;
import com.knet.goods.model.dto.resp.CreateKnetProductResp;
import com.knet.goods.model.dto.resp.OffSaleKnetProductResp;
import com.knet.goods.model.dto.resp.QueryKnetProductResp;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.service.IApiGoodsService;
import com.knet.goods.service.IKnetProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/25 16:38
 * @description:
 */
@Slf4j
@Service
public class ApiGoodsServiceImpl implements IApiGoodsService {
    @Resource
    private IKnetProductService iKnetProductService;
    @Resource
    @Qualifier("goodsThreadPoolExecutor")
    private ThreadPoolExecutor goodsThreadPoolExecutor;

    @DistributedLock(key = "'createProducts:' + #request.hashCode()", expire = 2)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateKnetProductResp createProducts(CreateKnetProductRequest request) {
        CreateKnetProductResp result = new CreateKnetProductResp();
        try {
            log.info("开始创建商品，请求参数: {}", request);
            // 1. 获取请求中的所有oneId
            List<String> requestOneIds = request.getProducts().stream()
                    .map(CreateKnetProductRequest.ProductDto::getOneId)
                    .filter(oneId -> oneId != null && !oneId.isEmpty())
                    .distinct()
                    .collect(Collectors.toList());
            // 2. 将已存在且状态为ON_SALE的oneId对应的商品设置为OFF_SALE
            if (!requestOneIds.isEmpty()) {
                log.info("将已存在的oneId对应商品设置为下架状态，oneIds: {}", requestOneIds);
                iKnetProductService.updateExistingProductsToOffSale(requestOneIds);
            }
            // 3. 创建新商品对象
            List<KnetProduct> saveProducts = request.getProducts().stream()
                    .map(productDto -> iKnetProductService.createByKnet(productDto))
                    .collect(Collectors.toList());
            // 4. 记录新生成的listingIds，用于后续查询
            List<String> preListingIds = saveProducts.stream()
                    .map(KnetProduct::getListingId)
                    .collect(Collectors.toList());
            // 5. 批量插入新商品
            log.info("批量插入新商品，数量: {}", saveProducts.size());
            iKnetProductService.insertIgnoreBatch(saveProducts);
            // 6. 查询成功插入的商品oneIds
            List<String> insertOneIds = iKnetProductService.queryByListingIds(preListingIds);
            log.info("成功插入的商品oneIds: {}", insertOneIds);
            // 7. 筛选出已经插入成功的商品，构建响应
            List<CreateKnetProductResp.ProductDto> createdProducts = saveProducts
                    .stream()
                    .filter(productDto -> insertOneIds.contains(productDto.getOneId()))
                    .map(KnetProduct::mapRespProductDto)
                    .collect(Collectors.toList());
            result.setProducts(createdProducts);
            log.info("创建商品完成，成功创建数量: {}", createdProducts.size());
            return result;
        } catch (Exception e) {
            log.error("创建商品失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @DistributedLock(key = "'offSale:' + #request.hashCode()", expire = 2)
    public OffSaleKnetProductResp offSale(OffSaleKnetProductRequest request) {
        List<String> listingIds = iKnetProductService.updateKnetProductForOffSale(request.getProducts());
        List<OffSaleKnetProductResp.ProductDto> products = iKnetProductService.getOffSaleListingIds(listingIds);
        return new OffSaleKnetProductResp(products);
    }

    @Override
    @DistributedLock(key = "'updatePrice:' + #request.hashCode()", expire = 2)
    public OffSaleKnetProductResp updatePrice(UpdatePriceKnetProductRequest request) {
        List<CompletableFuture<OffSaleKnetProductResp.ProductDto>> completableFutures = request.getProducts()
                .parallelStream()
                .map(productDto ->
                        CompletableFuture.supplyAsync(
                                () -> iKnetProductService.processKnetProductPrice(productDto), goodsThreadPoolExecutor)
                )
                .collect(Collectors.toList());
        List<OffSaleKnetProductResp.ProductDto> results = completableFutures
                .stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
        return new OffSaleKnetProductResp(results);
    }

    @Override
    public List<QueryKnetProductResp> queryKnetProduct(QueryKnetProductRequest request) {
        return iKnetProductService.queryKnetProductForApi(request);
    }
}
