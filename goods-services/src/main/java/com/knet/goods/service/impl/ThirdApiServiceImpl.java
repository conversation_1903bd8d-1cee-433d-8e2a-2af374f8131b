package com.knet.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpStatus;
import com.knet.common.base.HttpResult;
import com.knet.goods.model.dto.third.KnetGroupGetInventoryReq;
import com.knet.goods.model.dto.third.KnetGroupGetSkuSizePlatformPriceReq;
import com.knet.goods.model.dto.third.resp.KnetInventoryDataVo;
import com.knet.goods.model.dto.third.resp.KnetProductMarketDataVo;
import com.knet.goods.openfeign.ApiKnetGroupService;
import com.knet.goods.service.IThirdApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 11:12
 * @description: 第三方API调用服务 服务实现类
 */
@Slf4j
@Service
public class ThirdApiServiceImpl implements IThirdApiService {
    @Resource
    ApiKnetGroupService apiKnetGroupService;

    @Override
    public List<KnetProductMarketDataVo> getProductMarketData(KnetGroupGetSkuSizePlatformPriceReq request) {
        log.info("从kG 获取 sku size 平台最低价格 request: {}", request);
        HttpResult<List<KnetProductMarketDataVo>> response;
        try {
            response = apiKnetGroupService.getProductMarketData(null, request);
        } catch (Exception e) {
            log.error("调用apiKnetGroupService.getProductMarketData时发生异常", e);
            return Collections.emptyList();
        }
        if (BeanUtil.isEmpty(response) || BeanUtil.isEmpty(response.getData())) {
            log.error("从kG 获取 sku size 平台最低价格失败(服务异常) response: {}", response);
            return Collections.emptyList();
        }
        //sku查询不到价格 code 500 没有权限 401
        if (HttpStatus.HTTP_OK != response.getCode()) {
            log.error("从kG 获取 sku size 平台最低价格失败 response: {}", response.getMsg());
            return Collections.emptyList();
        }
        log.info("从kG 获取 sku size 平台最低价格 success response: {}", response);
        return response.getData();
    }

    @Override
    public List<KnetProductMarketDataVo> getHotSkuRankData() {
        HttpResult<List<KnetProductMarketDataVo>> response;
        try {
            response = apiKnetGroupService.getHotSkuRankData(null);
        } catch (Exception e) {
            log.error("调用apiKnetGroupService.getHotSkuRankData", e);
            return Collections.emptyList();
        }
        if (BeanUtil.isEmpty(response) || BeanUtil.isEmpty(response.getData())) {
            log.error("从KG 获取 kg 热门 sku 排行 失败(服务异常) response: {}", response);
            return Collections.emptyList();
        }
        if (HttpStatus.HTTP_OK != response.getCode()) {
            log.error("从KG 获取 kg 热门 sku 排行 失败 response: {}", response.getMsg());
            return Collections.emptyList();
        }
        log.info("从KG 获取 kg 热门 sku 排行 success response: {}", response);
        return response.getData();
    }

    @Override
    public List<KnetInventoryDataVo> getInventoryData(KnetGroupGetInventoryReq request) {
        log.info("从KG 获取商品库存数据 request: {}", request);
        HttpResult<List<KnetInventoryDataVo>> response;
        try {
            response = apiKnetGroupService.getInventoryData(null, request);
        } catch (Exception e) {
            log.error("调用apiKnetGroupService.getInventoryData时发生异常", e);
            return Collections.emptyList();
        }
        if (BeanUtil.isEmpty(response) || BeanUtil.isEmpty(response.getData())) {
            log.error("从KG 获取商品库存数据失败(服务异常) response: {}", response);
            return Collections.emptyList();
        }
        if (HttpStatus.HTTP_OK != response.getCode()) {
            log.error("从KG 获取商品库存数据失败 response: {}", response.getMsg());
            return Collections.emptyList();
        }
        log.info("从KG 获取商品库存数据 success, 数量: {}", response.getData().size());
        return response.getData();
    }
}
