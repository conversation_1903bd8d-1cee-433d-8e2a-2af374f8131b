package com.knet.goods.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.common.constants.SystemConstant;
import com.knet.goods.model.dto.third.KnetGroupGetInventoryReq;
import com.knet.goods.model.dto.third.KnetGroupGetSkuSizePlatformPriceReq;
import com.knet.goods.model.dto.third.resp.KnetInventoryDataVo;
import com.knet.goods.model.dto.third.resp.KnetProductMarketDataVo;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 11:02
 * @description: kj 服务提供API
 */
@RefreshScope
@FeignClient(name = "knetGroup-services", url = "${feign.client.config.knetGroupServices.url}")
public interface ApiKnetGroupService {

    /**
     * 从kg 获取商品市场数据
     *
     * @param token 自定义请求头 token
     * @param req   请求体
     * @return 商品市场数据
     * @see com.knet.goods.model.dto.third.KnetGroupGetSkuSizePlatformPriceReq
     */
    @PostMapping("/api/system/get_prod_market_data")
    HttpResult<List<KnetProductMarketDataVo>> getProductMarketData(@RequestHeader(SystemConstant.TOKEN) String token, @RequestBody KnetGroupGetSkuSizePlatformPriceReq req);

    /**
     * 获取 kg 热门 sku 排行
     *
     * @param token 自定义请求头 token
     * @return kg 热门 sku 排行
     */
    @GetMapping(value = "/api/system/get_hot_sku", consumes = MediaType.APPLICATION_JSON_VALUE)
    HttpResult<List<KnetProductMarketDataVo>> getHotSkuRankData(@RequestHeader(value = SystemConstant.TOKEN, required = false) String token);

    /**
     * 从kg 获取商品库存数据
     *
     * @param token 自定义请求头 token
     * @param req   请求体
     * @return 商品库存数据
     */
    @PostMapping("/api/system/get_inventory_data")
    HttpResult<List<KnetInventoryDataVo>> getInventoryData(@RequestHeader(SystemConstant.TOKEN) String token, @RequestBody KnetGroupGetInventoryReq req);
}
