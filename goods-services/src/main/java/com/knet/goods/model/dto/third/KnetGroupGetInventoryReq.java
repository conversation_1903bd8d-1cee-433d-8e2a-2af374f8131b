package com.knet.goods.model.dto.third;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 14:30
 * @description: 获取库存数据请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnetGroupGetInventoryReq extends BaseRequest {

    @Schema(description = "SKU列表，为空则获取所有SKU的库存数据")
    private List<String> skus;
}
