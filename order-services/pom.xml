<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.knet</groupId>
        <artifactId>knet</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <groupId>com.knet.order</groupId>
    <artifactId>order-services</artifactId>
    <packaging>jar</packaging>
    <name>order-services</name>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!--引入knet-common 包（包含所有公共依赖）-->
        <dependency>
            <groupId>com.knet.common</groupId>
            <artifactId>common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- ========== 编译时依赖 ========== -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- ========== 模块特有依赖 ========== -->
        <!--原生 rabbitmq-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
    </dependencies>
    <!--环境配置文件-->
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <packaging>jar</packaging>
                <profileActive>dev</profileActive>
                <appname>b2b-services-dev</appname>
            </properties>
        </profile>
        <profile>
            <id>prd</id>
            <properties>
                <packaging>jar</packaging>
                <profileActive>prd</profileActive>
                <appname>b2b-services-prd</appname>
            </properties>
        </profile>
    </profiles>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!--指定主启动类-->
                    <mainClass>com.knet.oauth.OrderServicesApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
