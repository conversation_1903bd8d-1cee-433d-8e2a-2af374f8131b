spring:
  profiles:
    active: dev
  config:
    import: optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  mvc:
    servlet:
      path: /orderService
  main:
    allow-circular-references: true
#actuator 运维配置信息
management:
  endpoints:
    web:
      exposure:
        include: "health,info"
mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  type-aliases-package: com.knet.entity
  #rocketmq:
  #  name-server: 192.168.6.5:9899  # 宿主机映射地址
  #  producer:
  #    group: order-producer-group
  #    send-message-timeout: 3000  # 发送超时3秒
  #  consumer:
  #    group: order-consumer-group
  #    max-reconsume-times: 3  # 最大重试次数