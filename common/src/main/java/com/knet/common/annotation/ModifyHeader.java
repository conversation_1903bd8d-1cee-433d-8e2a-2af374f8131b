package com.knet.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.knet.common.constants.SystemConstant.X_API_KEY;

/**
 * <AUTHOR>
 * @date 2025/4/9 14:59
 * @description: 获取自定义请求头ModifyHeader
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModifyHeader {
    String value() default X_API_KEY;  // 默认请求头名称，可自定义

    boolean required() default true;     // 是否必填
}
