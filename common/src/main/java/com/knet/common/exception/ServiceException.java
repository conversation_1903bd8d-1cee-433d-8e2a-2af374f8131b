package com.knet.common.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/13 15:52
 * @description: 服务统一异常
 */
@Getter
public class ServiceException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private int statusCode;

    public ServiceException(String message) {
        super(message);
        this.statusCode = 500;
    }

    public ServiceException(String message, Throwable cause) {
        super(message, cause);
        this.statusCode = 500;
    }
}
