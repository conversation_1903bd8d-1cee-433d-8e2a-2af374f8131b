# RabbitMQ配置和使用指南

## 问题描述
user-services模块启动时出现RabbitMQ连接失败错误，这是因为添加了RabbitMQ依赖但本地没有运行RabbitMQ服务。

## 解决方案

### 1. 开发环境（推荐）
在开发环境中，我们已经配置了RabbitMQ禁用选项：

**application-dev.yml配置：**
```yaml
# 开发环境配置
spring:
  rabbitmq:
    enabled: false  # 开发环境禁用RabbitMQ

# 禁用RabbitMQ健康检查
management:
  health:
    rabbit:
      enabled: false
```

### 2. 启用RabbitMQ（生产环境）
当需要启用RabbitMQ功能时，修改配置：

**application.yml配置：**
```yaml
spring:
  rabbitmq:
    enabled: true   # 启用RabbitMQ
    host: ***********
    port: 5672
    username: admin
    password: admin
    connection-timeout: 60000
    publisher-confirm-type: correlated
    template:
      retry:
        enabled: true
        max-attempts: 3
        initial-interval: 1000ms
    listener:
      simple:
        acknowledge-mode: manual
        prefetch: 1
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000ms
```

### 3. 本地安装RabbitMQ（可选）

#### 使用Docker安装：
```bash
# 拉取RabbitMQ镜像（带管理界面）
docker pull rabbitmq:3.11-management

# 运行RabbitMQ容器
docker run -d \
  --name rabbitmq \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=admin \
  rabbitmq:3.11-management

# 访问管理界面
# http://localhost:15672
# 用户名：admin，密码：admin
```

#### 使用Homebrew安装（macOS）：
```bash
# 安装RabbitMQ
brew install rabbitmq

# 启动RabbitMQ服务
brew services start rabbitmq

# 启用管理插件
rabbitmq-plugins enable rabbitmq_management
```

### 4. 功能特性

#### 条件化启用
- 消费者类使用`@ConditionalOnProperty`注解，只有在`spring.rabbitmq.enabled=true`时才启用
- 健康检查器会在应用启动时检查RabbitMQ连接状态

#### 消息处理流程
1. **payment-services** 发送用户操作记录消息到RabbitMQ
2. **user-services** 消费消息并保存到数据库
3. 支持消息重试和死信队列处理

#### 错误处理
- 消息发送失败不影响主业务流程
- 消费失败支持重试机制（最多3次）
- 超过重试次数的消息会发送到死信队列

### 5. 监控和调试

#### 日志配置
```yaml
logging:
  level:
    com.knet.user.mq: DEBUG
    org.springframework.amqp: WARN
    org.springframework.boot.actuate.amqp: WARN
```

#### 队列信息
- **交换机**: `user-operation-exchange`
- **队列**: `user-operation-queue.user-services`
- **路由键**: `user.operation.*`
- **死信交换机**: `DLX`
- **死信队列**: `dlx.user.operation.queue`

### 6. 启动检查

应用启动时会显示RabbitMQ连接状态：
- ✅ RabbitMQ连接检查成功
- ⚠️ RabbitMQ连接检查失败，但应用将继续运行
- 💡 提示：如需启用RabbitMQ功能，请确保RabbitMQ服务正在运行

### 7. 切换环境

#### 开发环境（禁用RabbitMQ）
```yaml
spring:
  profiles:
    active: dev
```

#### 生产环境（启用RabbitMQ）
```yaml
spring:
  profiles:
    active: prd
  rabbitmq:
    enabled: true
```

## 总结

通过以上配置，user-services模块可以在没有RabbitMQ的环境中正常启动，同时保留了在生产环境中启用RabbitMQ功能的能力。这种设计既保证了开发环境的便利性，又确保了生产环境的完整功能。
